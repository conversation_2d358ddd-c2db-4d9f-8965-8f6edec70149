"""
Configuration settings for Smart Visual Generation System v2.0
"""
import os
from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables and .env file"""
    
    # Model Storage
    model_cache_dir: str = "./models"
    huggingface_cache_dir: str = "./models/huggingface"
    
    # Device Configuration
    default_device: str = "cuda"
    enable_cpu_offload: bool = True
    gpu_memory_limit: int = 12
    
    # Server Configuration
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    
    # Model Settings
    sdxl_model_id: str = "stabilityai/stable-diffusion-xl-base-1.0"
    sdxl_refiner_id: str = "stabilityai/stable-diffusion-xl-refiner-1.0"
    sam_model_type: str = "vit_h"
    sam_checkpoint: str = "sam_vit_h_4b8939.pth"
    midas_model_type: str = "DPT_Hybrid"
    
    # Generation Settings
    default_steps: int = 30
    default_guidance_scale: float = 7.5
    default_width: int = 1024
    default_height: int = 1024
    max_width: int = 1536
    max_height: int = 1536
    
    # Safety and Performance
    enable_safety_checker: bool = True
    max_batch_size: int = 1
    memory_efficient_attention: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    @property
    def model_cache_path(self) -> Path:
        """Get model cache directory as Path object"""
        return Path(self.model_cache_dir)
    
    @property
    def sam_checkpoint_path(self) -> Path:
        """Get SAM checkpoint path"""
        return self.model_cache_path / "sam" / self.sam_checkpoint
    
    def ensure_directories(self):
        """Create necessary directories if they don't exist"""
        self.model_cache_path.mkdir(parents=True, exist_ok=True)
        (self.model_cache_path / "sam").mkdir(parents=True, exist_ok=True)
        (self.model_cache_path / "midas").mkdir(parents=True, exist_ok=True)
        Path(self.huggingface_cache_dir).mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings()
