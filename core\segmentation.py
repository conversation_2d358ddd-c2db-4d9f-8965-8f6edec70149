"""
Image segmentation using Segment Anything Model (SAM)
"""
import torch
import numpy as np
from PIL import Image
from segment_anything import sam_model_registry, SamPredictor, SamAutomaticMaskGenerator
from typing import List, Dict, Optional, Tuple, Union
import logging
from config.settings import settings
from core.utils import setup_device, pil_to_numpy, numpy_to_pil, clear_gpu_memory

logger = logging.getLogger(__name__)


class ImageSegmenter:
    """SAM-based image segmentation"""
    
    def __init__(self, device: Optional[torch.device] = None):
        self.device = device or setup_device(settings.default_device)
        self.sam_model = None
        self.predictor = None
        self.mask_generator = None
        self._load_model()
    
    def _load_model(self):
        """Load SAM model"""
        try:
            logger.info(f"Loading SAM model ({settings.sam_model_type})...")
            
            # Check if checkpoint exists
            if not settings.sam_checkpoint_path.exists():
                raise FileNotFoundError(
                    f"SAM checkpoint not found at {settings.sam_checkpoint_path}. "
                    "Please run 'python scripts/download_models.py' first."
                )
            
            # Load SAM model
            self.sam_model = sam_model_registry[settings.sam_model_type](
                checkpoint=str(settings.sam_checkpoint_path)
            )
            self.sam_model.to(device=self.device)
            
            # Initialize predictor for interactive segmentation
            self.predictor = SamPredictor(self.sam_model)
            
            # Initialize automatic mask generator
            self.mask_generator = SamAutomaticMaskGenerator(
                model=self.sam_model,
                points_per_side=32,
                pred_iou_thresh=0.86,
                stability_score_thresh=0.92,
                crop_n_layers=1,
                crop_n_points_downscale_factor=2,
                min_mask_region_area=100,
            )
            
            logger.info("SAM model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load SAM model: {e}")
            raise
    
    def segment_everything(self, image: Image.Image) -> List[Dict]:
        """
        Automatically segment all objects in the image
        
        Args:
            image: PIL Image to segment
            
        Returns:
            List of mask dictionaries with segmentation info
        """
        try:
            logger.info("Performing automatic segmentation...")
            
            # Convert PIL to numpy array
            image_array = pil_to_numpy(image)
            if image_array.shape[-1] == 4:  # RGBA
                image_array = image_array[:, :, :3]  # Remove alpha channel
            
            # Generate masks
            masks = self.mask_generator.generate(image_array)
            
            # Sort by area (largest first)
            masks = sorted(masks, key=lambda x: x['area'], reverse=True)
            
            logger.info(f"Found {len(masks)} segments")
            return masks
            
        except Exception as e:
            logger.error(f"Failed to segment image: {e}")
            raise
        finally:
            clear_gpu_memory()
    
    def segment_with_points(
        self,
        image: Image.Image,
        points: List[Tuple[int, int]],
        labels: List[int],
        box: Optional[Tuple[int, int, int, int]] = None
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Segment image using point prompts
        
        Args:
            image: PIL Image to segment
            points: List of (x, y) coordinates
            labels: List of labels (1 for foreground, 0 for background)
            box: Optional bounding box (x1, y1, x2, y2)
            
        Returns:
            Tuple of (masks, scores, logits)
        """
        try:
            logger.info(f"Segmenting with {len(points)} point(s)...")
            
            # Convert PIL to numpy array
            image_array = pil_to_numpy(image)
            if image_array.shape[-1] == 4:  # RGBA
                image_array = image_array[:, :, :3]  # Remove alpha channel
            
            # Set image for predictor
            self.predictor.set_image(image_array)
            
            # Convert inputs to numpy arrays
            input_points = np.array(points)
            input_labels = np.array(labels)
            input_box = np.array(box) if box else None
            
            # Predict masks
            masks, scores, logits = self.predictor.predict(
                point_coords=input_points,
                point_labels=input_labels,
                box=input_box,
                multimask_output=True
            )
            
            logger.info(f"Generated {len(masks)} mask(s)")
            return masks, scores, logits
            
        except Exception as e:
            logger.error(f"Failed to segment with points: {e}")
            raise
        finally:
            clear_gpu_memory()
    
    def segment_with_box(
        self,
        image: Image.Image,
        box: Tuple[int, int, int, int]
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Segment image using bounding box prompt
        
        Args:
            image: PIL Image to segment
            box: Bounding box (x1, y1, x2, y2)
            
        Returns:
            Tuple of (masks, scores, logits)
        """
        try:
            logger.info("Segmenting with bounding box...")
            
            # Convert PIL to numpy array
            image_array = pil_to_numpy(image)
            if image_array.shape[-1] == 4:  # RGBA
                image_array = image_array[:, :, :3]  # Remove alpha channel
            
            # Set image for predictor
            self.predictor.set_image(image_array)
            
            # Convert box to numpy array
            input_box = np.array(box)
            
            # Predict masks
            masks, scores, logits = self.predictor.predict(
                box=input_box,
                multimask_output=True
            )
            
            logger.info(f"Generated {len(masks)} mask(s)")
            return masks, scores, logits
            
        except Exception as e:
            logger.error(f"Failed to segment with box: {e}")
            raise
        finally:
            clear_gpu_memory()
    
    def create_mask_overlay(
        self,
        image: Image.Image,
        mask: np.ndarray,
        color: Tuple[int, int, int] = (255, 0, 0),
        alpha: float = 0.5
    ) -> Image.Image:
        """
        Create an overlay of the mask on the original image
        
        Args:
            image: Original PIL Image
            mask: Binary mask array
            color: RGB color for the mask overlay
            alpha: Transparency of the overlay
            
        Returns:
            PIL Image with mask overlay
        """
        # Convert image to RGBA
        image_rgba = image.convert('RGBA')
        
        # Create colored mask
        mask_colored = np.zeros((*mask.shape, 4), dtype=np.uint8)
        mask_colored[mask] = (*color, int(255 * alpha))
        
        # Convert mask to PIL Image
        mask_image = Image.fromarray(mask_colored, 'RGBA')
        
        # Composite the images
        result = Image.alpha_composite(image_rgba, mask_image)
        
        return result.convert('RGB')
    
    def extract_object(self, image: Image.Image, mask: np.ndarray) -> Image.Image:
        """
        Extract object using mask (set background to transparent)
        
        Args:
            image: Original PIL Image
            mask: Binary mask array
            
        Returns:
            PIL Image with transparent background
        """
        # Convert to RGBA
        image_array = pil_to_numpy(image.convert('RGBA'))
        
        # Apply mask to alpha channel
        image_array[:, :, 3] = mask.astype(np.uint8) * 255
        
        return numpy_to_pil(image_array).convert('RGBA')
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        clear_gpu_memory()
