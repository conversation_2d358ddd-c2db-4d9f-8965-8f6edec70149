"""
Image editing using Grounded-SAM + SDXL inpainting
"""
import torch
import numpy as np
from PIL import Image, ImageDraw
from diffusers import StableDiffusionXLInpaintPipeline
from typing import Optional, List, Tuple, Union
import logging
from config.settings import settings
from core.utils import setup_device, pil_to_numpy, numpy_to_pil, clear_gpu_memory, ensure_rgb
from core.segmentation import ImageSegmenter

logger = logging.getLogger(__name__)


class ImageEditor:
    """Grounded-SAM + SDXL inpainting for precise image editing"""
    
    def __init__(self, device: Optional[torch.device] = None):
        self.device = device or setup_device(settings.default_device)
        self.inpaint_pipeline = None
        self.segmenter = None
        self._load_models()
    
    def _load_models(self):
        """Load inpainting pipeline and segmentation model"""
        try:
            logger.info("Loading SDXL inpainting pipeline...")
            
            # Load SDXL inpainting pipeline
            self.inpaint_pipeline = StableDiffusionXLInpaintPipeline.from_pretrained(
                "diffusers/stable-diffusion-xl-1.0-inpainting-0.1",
                torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
                use_safetensors=True,
                variant="fp16" if self.device.type == "cuda" else None,
                cache_dir=settings.huggingface_cache_dir
            )
            
            # Enable memory efficient attention
            if settings.memory_efficient_attention:
                self.inpaint_pipeline.enable_attention_slicing()
                if hasattr(self.inpaint_pipeline, 'enable_xformers_memory_efficient_attention'):
                    try:
                        self.inpaint_pipeline.enable_xformers_memory_efficient_attention()
                        logger.info("Enabled xformers memory efficient attention")
                    except Exception as e:
                        logger.warning(f"Could not enable xformers: {e}")
            
            # Move to device or enable CPU offload
            if settings.enable_cpu_offload and self.device.type == "cuda":
                self.inpaint_pipeline.enable_model_cpu_offload()
                logger.info("Enabled CPU offload for inpainting pipeline")
            else:
                self.inpaint_pipeline = self.inpaint_pipeline.to(self.device)
            
            # Initialize segmenter
            self.segmenter = ImageSegmenter(device=self.device)
            
            logger.info("Image editing models loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load image editing models: {e}")
            raise
    
    def inpaint_region(
        self,
        image: Image.Image,
        mask: Union[Image.Image, np.ndarray],
        prompt: str,
        negative_prompt: Optional[str] = None,
        num_inference_steps: int = None,
        guidance_scale: float = None,
        strength: float = 1.0,
        seed: Optional[int] = None
    ) -> Image.Image:
        """
        Inpaint a masked region with new content
        
        Args:
            image: Original PIL Image
            mask: Mask as PIL Image or numpy array (white = inpaint, black = keep)
            prompt: Text description of what to generate in the masked area
            negative_prompt: What to avoid generating
            num_inference_steps: Number of denoising steps
            guidance_scale: How closely to follow the prompt
            strength: How much to change the masked area (0-1)
            seed: Random seed for reproducibility
            
        Returns:
            Inpainted PIL Image
        """
        try:
            logger.info(f"Inpainting with prompt: '{prompt[:50]}...'")
            
            # Ensure image is RGB
            image = ensure_rgb(image)
            
            # Convert mask to PIL Image if numpy array
            if isinstance(mask, np.ndarray):
                mask = numpy_to_pil(mask.astype(np.uint8) * 255)
            
            # Ensure mask is grayscale
            if mask.mode != 'L':
                mask = mask.convert('L')
            
            # Resize mask to match image if needed
            if mask.size != image.size:
                mask = mask.resize(image.size, Image.Resampling.LANCZOS)
            
            # Set defaults
            num_inference_steps = num_inference_steps or settings.default_steps
            guidance_scale = guidance_scale or settings.default_guidance_scale
            
            # Set seed if provided
            generator = None
            if seed is not None:
                generator = torch.Generator(device=self.device).manual_seed(seed)
            
            # Perform inpainting
            result = self.inpaint_pipeline(
                prompt=prompt,
                negative_prompt=negative_prompt,
                image=image,
                mask_image=mask,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                strength=strength,
                generator=generator
            )
            
            logger.info("Inpainting completed successfully")
            return result.images[0]
            
        except Exception as e:
            logger.error(f"Failed to inpaint region: {e}")
            raise
        finally:
            clear_gpu_memory()
    
    def remove_object(
        self,
        image: Image.Image,
        points: List[Tuple[int, int]],
        labels: List[int],
        inpaint_prompt: str = "background, natural, seamless",
        negative_prompt: str = "object, person, artifact"
    ) -> Tuple[Image.Image, Image.Image]:
        """
        Remove object using point-based segmentation and inpainting
        
        Args:
            image: Original PIL Image
            points: List of (x, y) coordinates for segmentation
            labels: List of labels (1 for object to remove, 0 for background)
            inpaint_prompt: Prompt for what to fill the removed area with
            negative_prompt: What to avoid in the inpainted area
            
        Returns:
            Tuple of (edited_image, mask_used)
        """
        try:
            logger.info("Removing object with point-based segmentation...")
            
            # Segment the object
            masks, scores, _ = self.segmenter.segment_with_points(
                image, points, labels
            )
            
            # Use the best mask (highest score)
            best_mask = masks[np.argmax(scores)]
            
            # Convert mask to PIL Image
            mask_image = numpy_to_pil((best_mask * 255).astype(np.uint8))
            
            # Inpaint the masked region
            edited_image = self.inpaint_region(
                image=image,
                mask=mask_image,
                prompt=inpaint_prompt,
                negative_prompt=negative_prompt
            )
            
            logger.info("Object removal completed")
            return edited_image, mask_image
            
        except Exception as e:
            logger.error(f"Failed to remove object: {e}")
            raise
    
    def replace_object(
        self,
        image: Image.Image,
        points: List[Tuple[int, int]],
        labels: List[int],
        replacement_prompt: str,
        negative_prompt: Optional[str] = None
    ) -> Tuple[Image.Image, Image.Image]:
        """
        Replace object using point-based segmentation and inpainting
        
        Args:
            image: Original PIL Image
            points: List of (x, y) coordinates for segmentation
            labels: List of labels (1 for object to replace, 0 for background)
            replacement_prompt: Prompt for what to replace the object with
            negative_prompt: What to avoid in the replacement
            
        Returns:
            Tuple of (edited_image, mask_used)
        """
        try:
            logger.info(f"Replacing object with: '{replacement_prompt[:50]}...'")
            
            # Segment the object
            masks, scores, _ = self.segmenter.segment_with_points(
                image, points, labels
            )
            
            # Use the best mask (highest score)
            best_mask = masks[np.argmax(scores)]
            
            # Convert mask to PIL Image
            mask_image = numpy_to_pil((best_mask * 255).astype(np.uint8))
            
            # Inpaint the masked region with replacement
            edited_image = self.inpaint_region(
                image=image,
                mask=mask_image,
                prompt=replacement_prompt,
                negative_prompt=negative_prompt
            )
            
            logger.info("Object replacement completed")
            return edited_image, mask_image
            
        except Exception as e:
            logger.error(f"Failed to replace object: {e}")
            raise
    
    def edit_with_box(
        self,
        image: Image.Image,
        box: Tuple[int, int, int, int],
        edit_prompt: str,
        negative_prompt: Optional[str] = None
    ) -> Tuple[Image.Image, Image.Image]:
        """
        Edit region defined by bounding box
        
        Args:
            image: Original PIL Image
            box: Bounding box (x1, y1, x2, y2)
            edit_prompt: Prompt for how to edit the region
            negative_prompt: What to avoid in the edit
            
        Returns:
            Tuple of (edited_image, mask_used)
        """
        try:
            logger.info(f"Editing box region with: '{edit_prompt[:50]}...'")
            
            # Segment using bounding box
            masks, scores, _ = self.segmenter.segment_with_box(image, box)
            
            # Use the best mask (highest score)
            best_mask = masks[np.argmax(scores)]
            
            # Convert mask to PIL Image
            mask_image = numpy_to_pil((best_mask * 255).astype(np.uint8))
            
            # Inpaint the masked region
            edited_image = self.inpaint_region(
                image=image,
                mask=mask_image,
                prompt=edit_prompt,
                negative_prompt=negative_prompt
            )
            
            logger.info("Box region editing completed")
            return edited_image, mask_image
            
        except Exception as e:
            logger.error(f"Failed to edit box region: {e}")
            raise
    
    def create_custom_mask(
        self,
        image_size: Tuple[int, int],
        shapes: List[Dict]
    ) -> Image.Image:
        """
        Create custom mask from shape definitions
        
        Args:
            image_size: (width, height) of the image
            shapes: List of shape dictionaries with 'type' and coordinates
            
        Returns:
            PIL Image mask
        """
        # Create blank mask
        mask = Image.new('L', image_size, 0)
        draw = ImageDraw.Draw(mask)
        
        for shape in shapes:
            if shape['type'] == 'circle':
                # Circle: {'type': 'circle', 'center': (x, y), 'radius': r}
                center = shape['center']
                radius = shape['radius']
                bbox = (
                    center[0] - radius,
                    center[1] - radius,
                    center[0] + radius,
                    center[1] + radius
                )
                draw.ellipse(bbox, fill=255)
                
            elif shape['type'] == 'rectangle':
                # Rectangle: {'type': 'rectangle', 'bbox': (x1, y1, x2, y2)}
                draw.rectangle(shape['bbox'], fill=255)
                
            elif shape['type'] == 'polygon':
                # Polygon: {'type': 'polygon', 'points': [(x1, y1), (x2, y2), ...]}
                draw.polygon(shape['points'], fill=255)
        
        return mask
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        clear_gpu_memory()
