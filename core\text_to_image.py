"""
Text-to-Image generation using Stable Diffusion XL (SDXL)
"""
import torch
from PIL import Image
from diffusers import Diffusion<PERSON><PERSON>eline, StableDiffusionXLPipeline
from typing import Optional, List, Union
import logging
from config.settings import settings
from core.utils import setup_device, clear_gpu_memory

logger = logging.getLogger(__name__)


class TextToImageGenerator:
    """SDXL-based text-to-image generator"""
    
    def __init__(self, device: Optional[torch.device] = None):
        self.device = device or setup_device(settings.default_device)
        self.pipeline = None
        self.refiner = None
        self._load_models()
    
    def _load_models(self):
        """Load SDXL base and refiner models"""
        try:
            logger.info("Loading SDXL base model...")
            
            # Load base model
            self.pipeline = StableDiffusionXLPipeline.from_pretrained(
                settings.sdxl_model_id,
                torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
                use_safetensors=True,
                variant="fp16" if self.device.type == "cuda" else None,
                cache_dir=settings.huggingface_cache_dir
            )
            
            # Enable memory efficient attention if configured
            if settings.memory_efficient_attention:
                self.pipeline.enable_attention_slicing()
                if hasattr(self.pipeline, 'enable_xformers_memory_efficient_attention'):
                    try:
                        self.pipeline.enable_xformers_memory_efficient_attention()
                        logger.info("Enabled xformers memory efficient attention")
                    except Exception as e:
                        logger.warning(f"Could not enable xformers: {e}")
            
            # Move to device or enable CPU offload
            if settings.enable_cpu_offload and self.device.type == "cuda":
                self.pipeline.enable_model_cpu_offload()
                logger.info("Enabled CPU offload for base model")
            else:
                self.pipeline = self.pipeline.to(self.device)
            
            # Load refiner model
            logger.info("Loading SDXL refiner model...")
            self.refiner = DiffusionPipeline.from_pretrained(
                settings.sdxl_refiner_id,
                text_encoder_2=self.pipeline.text_encoder_2,
                vae=self.pipeline.vae,
                torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
                use_safetensors=True,
                variant="fp16" if self.device.type == "cuda" else None,
                cache_dir=settings.huggingface_cache_dir
            )
            
            if settings.enable_cpu_offload and self.device.type == "cuda":
                self.refiner.enable_model_cpu_offload()
                logger.info("Enabled CPU offload for refiner model")
            else:
                self.refiner = self.refiner.to(self.device)
            
            logger.info("SDXL models loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load SDXL models: {e}")
            raise
    
    def generate(
        self,
        prompt: str,
        negative_prompt: Optional[str] = None,
        width: int = None,
        height: int = None,
        num_inference_steps: int = None,
        guidance_scale: float = None,
        num_images: int = 1,
        seed: Optional[int] = None,
        use_refiner: bool = True
    ) -> List[Image.Image]:
        """
        Generate images from text prompt
        
        Args:
            prompt: Text description of desired image
            negative_prompt: What to avoid in the image
            width: Image width (default from settings)
            height: Image height (default from settings)
            num_inference_steps: Number of denoising steps
            guidance_scale: How closely to follow the prompt
            num_images: Number of images to generate
            seed: Random seed for reproducibility
            use_refiner: Whether to use the refiner model
            
        Returns:
            List of generated PIL Images
        """
        # Set defaults
        width = width or settings.default_width
        height = height or settings.default_height
        num_inference_steps = num_inference_steps or settings.default_steps
        guidance_scale = guidance_scale or settings.default_guidance_scale
        
        # Validate dimensions
        width = min(width, settings.max_width)
        height = min(height, settings.max_height)
        
        # Set seed if provided
        generator = None
        if seed is not None:
            generator = torch.Generator(device=self.device).manual_seed(seed)
        
        try:
            logger.info(f"Generating {num_images} image(s) with prompt: '{prompt[:50]}...'")
            
            # Generate with base model
            if use_refiner:
                # Use high noise for base, refiner will clean up
                base_steps = max(1, int(num_inference_steps * 0.8))
                
                images = self.pipeline(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    width=width,
                    height=height,
                    num_inference_steps=base_steps,
                    guidance_scale=guidance_scale,
                    num_images_per_prompt=num_images,
                    generator=generator,
                    output_type="latent",
                    denoising_end=0.8
                ).images
                
                # Refine the images
                refined_images = self.refiner(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    image=images,
                    num_inference_steps=num_inference_steps,
                    guidance_scale=guidance_scale,
                    denoising_start=0.8,
                    generator=generator
                ).images
                
                result_images = refined_images
                
            else:
                # Generate with base model only
                result = self.pipeline(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    width=width,
                    height=height,
                    num_inference_steps=num_inference_steps,
                    guidance_scale=guidance_scale,
                    num_images_per_prompt=num_images,
                    generator=generator
                )
                result_images = result.images
            
            logger.info(f"Successfully generated {len(result_images)} image(s)")
            return result_images
            
        except Exception as e:
            logger.error(f"Failed to generate image: {e}")
            raise
        finally:
            # Clear GPU memory
            clear_gpu_memory()
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        clear_gpu_memory()
