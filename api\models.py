"""
Pydantic models for API request/response validation
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Tuple, Dict, Any, Union
from enum import Enum


class DeviceType(str, Enum):
    """Available device types"""
    cuda = "cuda"
    mps = "mps"
    cpu = "cpu"


class ColorMapType(str, Enum):
    """Available OpenCV colormaps for depth visualization"""
    plasma = "COLORMAP_PLASMA"
    viridis = "COLORMAP_VIRIDIS"
    jet = "COLORMAP_JET"
    hot = "COLORMAP_HOT"
    cool = "COLORMAP_COOL"
    spring = "COLORMAP_SPRING"
    summer = "COLORMAP_SUMMER"
    autumn = "COLORMAP_AUTUMN"
    winter = "COLORMAP_WINTER"


# Text-to-Image Models
class GenerateRequest(BaseModel):
    """Request model for text-to-image generation"""
    prompt: str = Field(..., description="Text description of desired image")
    negative_prompt: Optional[str] = Field(None, description="What to avoid in the image")
    width: Optional[int] = Field(1024, ge=256, le=1536, description="Image width")
    height: Optional[int] = Field(1024, ge=256, le=1536, description="Image height")
    num_inference_steps: Optional[int] = Field(30, ge=10, le=100, description="Number of denoising steps")
    guidance_scale: Optional[float] = Field(7.5, ge=1.0, le=20.0, description="Guidance scale")
    num_images: Optional[int] = Field(1, ge=1, le=4, description="Number of images to generate")
    seed: Optional[int] = Field(None, description="Random seed for reproducibility")
    use_refiner: Optional[bool] = Field(True, description="Whether to use SDXL refiner")


class GenerateResponse(BaseModel):
    """Response model for text-to-image generation"""
    images: List[str] = Field(..., description="Base64 encoded generated images")
    seed_used: Optional[int] = Field(None, description="Seed that was used")
    generation_time: float = Field(..., description="Time taken to generate in seconds")


# Segmentation Models
class SegmentRequest(BaseModel):
    """Request model for image segmentation"""
    image: str = Field(..., description="Base64 encoded input image")
    mode: str = Field("auto", description="Segmentation mode: 'auto', 'points', 'box'")
    points: Optional[List[Tuple[int, int]]] = Field(None, description="Points for segmentation (x, y)")
    labels: Optional[List[int]] = Field(None, description="Labels for points (1=foreground, 0=background)")
    box: Optional[Tuple[int, int, int, int]] = Field(None, description="Bounding box (x1, y1, x2, y2)")
    return_overlay: Optional[bool] = Field(True, description="Whether to return overlay visualization")


class SegmentResponse(BaseModel):
    """Response model for image segmentation"""
    masks: List[str] = Field(..., description="Base64 encoded mask images")
    overlay: Optional[str] = Field(None, description="Base64 encoded overlay visualization")
    num_segments: int = Field(..., description="Number of segments found")
    processing_time: float = Field(..., description="Time taken to process in seconds")


# Image Editing Models
class EditRequest(BaseModel):
    """Request model for image editing"""
    image: str = Field(..., description="Base64 encoded input image")
    mode: str = Field("inpaint", description="Edit mode: 'inpaint', 'remove', 'replace'")
    prompt: str = Field(..., description="Edit prompt describing the desired change")
    negative_prompt: Optional[str] = Field(None, description="What to avoid in the edit")
    
    # Mask specification (one of these should be provided)
    mask: Optional[str] = Field(None, description="Base64 encoded mask image")
    points: Optional[List[Tuple[int, int]]] = Field(None, description="Points for segmentation")
    labels: Optional[List[int]] = Field(None, description="Labels for points")
    box: Optional[Tuple[int, int, int, int]] = Field(None, description="Bounding box")
    
    # Generation parameters
    num_inference_steps: Optional[int] = Field(30, ge=10, le=100)
    guidance_scale: Optional[float] = Field(7.5, ge=1.0, le=20.0)
    strength: Optional[float] = Field(1.0, ge=0.1, le=1.0, description="Edit strength")
    seed: Optional[int] = Field(None, description="Random seed")


class EditResponse(BaseModel):
    """Response model for image editing"""
    edited_image: str = Field(..., description="Base64 encoded edited image")
    mask_used: str = Field(..., description="Base64 encoded mask that was used")
    processing_time: float = Field(..., description="Time taken to process in seconds")


# Depth Estimation Models
class DepthRequest(BaseModel):
    """Request model for depth estimation"""
    image: str = Field(..., description="Base64 encoded input image")
    output_width: Optional[int] = Field(None, ge=256, le=1536, description="Output width")
    output_height: Optional[int] = Field(None, ge=256, le=1536, description="Output height")
    colormap: Optional[ColorMapType] = Field(ColorMapType.plasma, description="Colormap for visualization")
    return_raw: Optional[bool] = Field(False, description="Whether to return raw depth values")
    create_3d_effect: Optional[bool] = Field(False, description="Whether to create 3D displacement effect")
    displacement_strength: Optional[float] = Field(10.0, ge=1.0, le=50.0, description="3D effect strength")


class DepthResponse(BaseModel):
    """Response model for depth estimation"""
    depth_image: str = Field(..., description="Base64 encoded colored depth map")
    raw_depth: Optional[str] = Field(None, description="Base64 encoded raw depth values (if requested)")
    effect_image: Optional[str] = Field(None, description="Base64 encoded 3D effect image (if requested)")
    processing_time: float = Field(..., description="Time taken to process in seconds")


# System Models
class HealthResponse(BaseModel):
    """Response model for health check"""
    status: str = Field(..., description="System status")
    device: str = Field(..., description="Current device being used")
    memory_info: Dict[str, Any] = Field(..., description="Memory usage information")
    models_loaded: Dict[str, bool] = Field(..., description="Status of loaded models")


class ErrorResponse(BaseModel):
    """Response model for errors"""
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    code: Optional[int] = Field(None, description="Error code")
