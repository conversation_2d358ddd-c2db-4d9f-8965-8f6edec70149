"""
Utility functions for Smart Visual Generation System v2.0
"""
import io
import base64
import torch
import numpy as np
from PIL import Image
from typing import Union, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


def setup_device(preferred_device: str = "cuda") -> torch.device:
    """
    Setup and return the best available device for computation
    
    Args:
        preferred_device: Preferred device ('cuda', 'mps', 'cpu')
        
    Returns:
        torch.device: The device to use
    """
    if preferred_device == "cuda" and torch.cuda.is_available():
        device = torch.device("cuda")
        logger.info(f"Using CUDA device: {torch.cuda.get_device_name()}")
        logger.info(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    elif preferred_device == "mps" and torch.backends.mps.is_available():
        device = torch.device("mps")
        logger.info("Using MPS (Apple Silicon) device")
    else:
        device = torch.device("cpu")
        logger.info("Using CPU device")
    
    return device


def pil_to_base64(image: Image.Image, format: str = "PNG") -> str:
    """
    Convert PIL Image to base64 string
    
    Args:
        image: PIL Image
        format: Image format (PNG, JPEG, etc.)
        
    Returns:
        Base64 encoded string
    """
    buffer = io.BytesIO()
    image.save(buffer, format=format)
    img_str = base64.b64encode(buffer.getvalue()).decode()
    return img_str


def base64_to_pil(base64_str: str) -> Image.Image:
    """
    Convert base64 string to PIL Image
    
    Args:
        base64_str: Base64 encoded image string
        
    Returns:
        PIL Image
    """
    img_data = base64.b64decode(base64_str)
    image = Image.open(io.BytesIO(img_data))
    return image


def numpy_to_pil(array: np.ndarray) -> Image.Image:
    """
    Convert numpy array to PIL Image
    
    Args:
        array: Numpy array (H, W, C) or (H, W)
        
    Returns:
        PIL Image
    """
    if array.dtype != np.uint8:
        # Normalize to 0-255 range
        array = ((array - array.min()) / (array.max() - array.min()) * 255).astype(np.uint8)
    
    if len(array.shape) == 2:
        # Grayscale
        return Image.fromarray(array, mode='L')
    elif len(array.shape) == 3:
        # RGB
        return Image.fromarray(array, mode='RGB')
    else:
        raise ValueError(f"Unsupported array shape: {array.shape}")


def pil_to_numpy(image: Image.Image) -> np.ndarray:
    """
    Convert PIL Image to numpy array
    
    Args:
        image: PIL Image
        
    Returns:
        Numpy array
    """
    return np.array(image)


def resize_image(image: Image.Image, max_size: Tuple[int, int], maintain_aspect: bool = True) -> Image.Image:
    """
    Resize image while optionally maintaining aspect ratio
    
    Args:
        image: PIL Image to resize
        max_size: Maximum (width, height)
        maintain_aspect: Whether to maintain aspect ratio
        
    Returns:
        Resized PIL Image
    """
    if maintain_aspect:
        image.thumbnail(max_size, Image.Resampling.LANCZOS)
        return image
    else:
        return image.resize(max_size, Image.Resampling.LANCZOS)


def ensure_rgb(image: Image.Image) -> Image.Image:
    """
    Ensure image is in RGB format
    
    Args:
        image: PIL Image
        
    Returns:
        RGB PIL Image
    """
    if image.mode != 'RGB':
        return image.convert('RGB')
    return image


def clear_gpu_memory():
    """Clear GPU memory cache"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()


def get_memory_usage() -> dict:
    """
    Get current memory usage information
    
    Returns:
        Dictionary with memory usage stats
    """
    memory_info = {}
    
    if torch.cuda.is_available():
        memory_info['gpu_allocated'] = torch.cuda.memory_allocated() / 1e9
        memory_info['gpu_reserved'] = torch.cuda.memory_reserved() / 1e9
        memory_info['gpu_max_allocated'] = torch.cuda.max_memory_allocated() / 1e9
    
    return memory_info
