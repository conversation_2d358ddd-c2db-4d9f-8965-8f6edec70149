# Smart Visual Generation System v2.0 - Setup Guide

This guide will walk you through setting up the Smart Visual Generation System v2.0 on your machine.

## 📋 Prerequisites

### System Requirements
- **Python**: 3.9 or higher
- **GPU**: NVIDIA GPU with 8GB+ VRAM (recommended)
- **RAM**: 16GB+ (32GB recommended)
- **Storage**: ~50GB free space for models
- **OS**: Windows, Linux, or macOS

### Check Your System
```bash
# Check Python version
python --version

# Check if you have NVIDIA GPU (Windows)
nvidia-smi

# Check available disk space
# Windows: dir
# Linux/macOS: df -h
```

## 🚀 Installation Steps

### Step 1: Clone and Setup Environment

```bash
# Navigate to your desired directory
cd /path/to/your/projects

# Create project directory (if not already done)
# mkdir smart_visual_gen_v2_python
# cd smart_visual_gen_v2_python

# Create virtual environment
python -m venv .venv

# Activate virtual environment
# Windows:
.venv\Scripts\activate
# Linux/macOS:
source .venv/bin/activate

# Verify virtual environment is active (should show (.venv) in prompt)
```

### Step 2: Install Dependencies

```bash
# Upgrade pip first
python -m pip install --upgrade pip

# Install PyTorch with CUDA support (if you have NVIDIA GPU)
# Visit https://pytorch.org/get-started/locally/ for the latest command
# For CUDA 11.8:
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# For CPU only (slower):
# pip install torch torchvision torchaudio

# Install other requirements
pip install -r requirements.txt
```

### Step 3: Test Installation

```bash
# Run installation test
python test_installation.py
```

This should show:
- ✅ All packages imported successfully
- ✅ PyTorch and CUDA information
- ✅ Configuration loaded
- ✅ API components imported
- ❌ Model files (expected at this point)

### Step 4: Download AI Models

```bash
# Download required models (this will take a while and use ~50GB)
python scripts/download_models.py
```

This will:
1. Download SAM (Segment Anything) model checkpoints (~2.5GB)
2. Ask if you want to download test models (~13GB for SDXL)
3. Set up model cache directories

**Note**: You can skip downloading test models initially. They will be downloaded automatically when first used.

### Step 5: Verify Complete Installation

```bash
# Run installation test again
python test_installation.py
```

Now all tests should pass:
- ✅ Package Imports
- ✅ PyTorch & CUDA
- ✅ Configuration
- ✅ API Components
- ✅ Model Files

## 🎯 Running the System

### Start the Server

```bash
# Make sure virtual environment is activated
# Windows: .venv\Scripts\activate
# Linux/macOS: source .venv/bin/activate

# Start the server
python main.py
```

You should see:
```
🎨 Smart Visual Generation System v2.0
==================================================
An AI-powered creative suite for:
  🎨 Text-to-Image Generation (SDXL)
  🧠 Automatic Segmentation (SAM)
  👆 Region Editing (Grounded-SAM + Inpainting)
  🌄 Depth Estimation (MiDaS)
==================================================
🚀 Starting server on 0.0.0.0:8000
📖 API docs: http://0.0.0.0:8000/docs
🔧 Device: cuda
```

### Test the API

Open a new terminal and run:

```bash
# Activate virtual environment in new terminal
# Windows: .venv\Scripts\activate
# Linux/macOS: source .venv/bin/activate

# Run API examples
python example_usage.py
```

Or visit the interactive API documentation:
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/
- **System Info**: http://localhost:8000/info

## 🔧 Troubleshooting

### Common Issues

#### 1. CUDA Out of Memory
```
RuntimeError: CUDA out of memory
```

**Solutions**:
- Edit `.env` file: `ENABLE_CPU_OFFLOAD=true`
- Reduce image size in requests
- Close other GPU applications
- Use smaller models

#### 2. Import Errors
```
ModuleNotFoundError: No module named 'torch'
```

**Solutions**:
- Make sure virtual environment is activated
- Reinstall requirements: `pip install -r requirements.txt --force-reinstall`
- Check Python version: `python --version` (should be 3.9+)

#### 3. Model Download Fails
```
Failed to download SAM models
```

**Solutions**:
- Check internet connection
- Ensure ~50GB free disk space
- Try running `python scripts/download_models.py` again
- Check firewall/antivirus settings

#### 4. Server Won't Start
```
Address already in use
```

**Solutions**:
- Change port in `.env`: `PORT=8001`
- Kill existing process: `taskkill /f /im python.exe` (Windows)
- Use different terminal

#### 5. Slow Generation
**Solutions**:
- Ensure GPU is being used (check `/info` endpoint)
- Reduce `num_inference_steps` in requests
- Use smaller image dimensions
- Enable CPU offload for memory-constrained systems

### Performance Optimization

#### For High-End Systems (RTX 4090, A100, etc.)
```env
# .env settings
ENABLE_CPU_OFFLOAD=false
MEMORY_EFFICIENT_ATTENTION=true
DEFAULT_STEPS=50
GPU_MEMORY_LIMIT=24
```

#### For Mid-Range Systems (RTX 3070, RTX 4060, etc.)
```env
# .env settings
ENABLE_CPU_OFFLOAD=true
MEMORY_EFFICIENT_ATTENTION=true
DEFAULT_STEPS=30
GPU_MEMORY_LIMIT=8
```

#### For CPU-Only Systems
```env
# .env settings
DEFAULT_DEVICE=cpu
ENABLE_CPU_OFFLOAD=false
DEFAULT_STEPS=20
```

## 📞 Getting Help

1. **Check logs**: Look at console output for detailed error messages
2. **System info**: Visit `http://localhost:8000/info` for diagnostics
3. **Test installation**: Run `python test_installation.py`
4. **Check requirements**: Ensure all prerequisites are met
5. **Update drivers**: Make sure GPU drivers are up to date

## 🎉 Next Steps

Once everything is working:

1. **Explore the API**: Visit http://localhost:8000/docs
2. **Run examples**: `python example_usage.py`
3. **Customize settings**: Edit `.env` file
4. **Build your app**: Use the API in your own applications
5. **Contribute**: Submit issues and improvements on GitHub

Happy creating! 🎨
