#!/usr/bin/env python3
"""
Download and setup AI models for Smart Visual Generation System v2.0
"""
import os
import sys
import requests
import hashlib
from pathlib import Path
from tqdm import tqdm
import logging

# Add parent directory to path to import config
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def download_file(url: str, filepath: Path, expected_hash: str = None) -> bool:
    """
    Download a file with progress bar and optional hash verification
    
    Args:
        url: URL to download from
        filepath: Local path to save file
        expected_hash: Expected SHA256 hash for verification
        
    Returns:
        True if download successful, False otherwise
    """
    try:
        # Create directory if it doesn't exist
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Check if file already exists and has correct hash
        if filepath.exists() and expected_hash:
            logger.info(f"Checking existing file: {filepath.name}")
            if verify_hash(filepath, expected_hash):
                logger.info(f"File already exists and verified: {filepath.name}")
                return True
            else:
                logger.warning(f"Existing file hash mismatch, re-downloading: {filepath.name}")
        
        logger.info(f"Downloading {filepath.name} from {url}")
        
        # Download with progress bar
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(filepath, 'wb') as f, tqdm(
            desc=filepath.name,
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    pbar.update(len(chunk))
        
        # Verify hash if provided
        if expected_hash:
            if verify_hash(filepath, expected_hash):
                logger.info(f"Download verified: {filepath.name}")
                return True
            else:
                logger.error(f"Hash verification failed: {filepath.name}")
                filepath.unlink()  # Delete corrupted file
                return False
        
        logger.info(f"Download completed: {filepath.name}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to download {url}: {e}")
        if filepath.exists():
            filepath.unlink()  # Clean up partial download
        return False


def verify_hash(filepath: Path, expected_hash: str) -> bool:
    """
    Verify SHA256 hash of a file
    
    Args:
        filepath: Path to file
        expected_hash: Expected SHA256 hash
        
    Returns:
        True if hash matches, False otherwise
    """
    try:
        sha256_hash = hashlib.sha256()
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        
        actual_hash = sha256_hash.hexdigest()
        return actual_hash.lower() == expected_hash.lower()
        
    except Exception as e:
        logger.error(f"Failed to verify hash for {filepath}: {e}")
        return False


def download_sam_models():
    """Download Segment Anything Model checkpoints"""
    logger.info("Downloading SAM models...")
    
    sam_models = {
        "sam_vit_h_4b8939.pth": {
            "url": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth",
            "hash": "a7bf3b02f3ebf1267aba913ff637d9a2d5c33d3173bb679e46d9f338c26f262e"
        },
        "sam_vit_l_0b3195.pth": {
            "url": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_l_0b3195.pth",
            "hash": "3adcc4315b642a4d2101128f611684e8734c41232a17c648ed1693702a49a622"
        },
        "sam_vit_b_01ec64.pth": {
            "url": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth",
            "hash": "ec2df62732614e57411cdcf32a23ffdf28910380d03139ee0f4fcbe91eb8c912"
        }
    }
    
    sam_dir = settings.model_cache_path / "sam"
    
    for filename, info in sam_models.items():
        filepath = sam_dir / filename
        success = download_file(info["url"], filepath, info["hash"])
        if not success:
            logger.error(f"Failed to download {filename}")
            return False
    
    logger.info("SAM models downloaded successfully")
    return True


def setup_huggingface_cache():
    """Setup Hugging Face cache directory"""
    logger.info("Setting up Hugging Face cache...")
    
    # Set environment variable for Hugging Face cache
    os.environ['TRANSFORMERS_CACHE'] = str(settings.huggingface_cache_dir)
    os.environ['HF_HOME'] = str(settings.huggingface_cache_dir)
    
    # Create cache directory
    Path(settings.huggingface_cache_dir).mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Hugging Face cache set to: {settings.huggingface_cache_dir}")


def download_test_models():
    """Download a minimal set of models for testing"""
    logger.info("Downloading test models (this may take a while)...")
    
    try:
        # Import here to avoid circular imports
        from diffusers import StableDiffusionXLPipeline
        from transformers import pipeline
        
        # Download SDXL base model (this will cache it)
        logger.info("Downloading SDXL base model...")
        pipe = StableDiffusionXLPipeline.from_pretrained(
            settings.sdxl_model_id,
            cache_dir=settings.huggingface_cache_dir,
            torch_dtype=None  # Use float32 for compatibility
        )
        del pipe  # Free memory
        
        logger.info("Test models downloaded successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to download test models: {e}")
        return False


def main():
    """Main function to download all required models"""
    logger.info("Starting model download for Smart Visual Generation System v2.0")
    
    # Ensure directories exist
    settings.ensure_directories()
    
    # Setup Hugging Face cache
    setup_huggingface_cache()
    
    # Download SAM models
    if not download_sam_models():
        logger.error("Failed to download SAM models")
        sys.exit(1)
    
    # Ask user if they want to download test models
    download_test = input("\nDownload test models now? This will download ~13GB of data. (y/N): ")
    if download_test.lower() in ['y', 'yes']:
        if not download_test_models():
            logger.error("Failed to download test models")
            sys.exit(1)
    else:
        logger.info("Skipping test model download. Models will be downloaded on first use.")
    
    logger.info("\n✅ Model setup completed successfully!")
    logger.info("You can now run: python main.py")


if __name__ == "__main__":
    main()
