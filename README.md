# Smart Visual Generation System v2.0 (Python-Only)

An AI-powered creative suite for intelligent image generation, segmentation, editing, and 3D depth enhancement — fully offline, open-source, and Python-only (no Node.js or browser frontend).

---

## ✨ Features

🎨 **Text-to-Image Generation** — Create high-quality images from text using Stable Diffusion XL (SDXL).

🧠 **Automatic Segmentation** — Detect and outline objects using Segment Anything Model (SAM).

👆 **Region Editing** — Edit, remove, or modify objects precisely with Grounded-SAM + SDXL inpainting.

🌄 **Depth Estimation** — Convert 2D images to depth maps with MiDaS (DPT Hybrid).

🔒 **Offline & Private** — All models run locally. No API keys, no data leaves your machine.

---

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- NVIDIA GPU with 8GB+ VRAM (CUDA recommended)
- 16GB+ RAM (32GB recommended)
- ~50GB disk space for models

---

### Installation

1. **Clone the repository:**
```bash
git clone <repo-url>
cd smart_visual_gen_v2_python
```

2. **Create virtual environment and install dependencies:**
```bash
python -m venv .venv
.venv\Scripts\activate        # Windows
source .venv/bin/activate     # Linux/macOS

pip install -r requirements.txt
```

3. **Download AI models:**
```bash
python scripts/download_models.py
```

4. **Start the server:**
```bash
python main.py
```

The server will start at `http://localhost:8000` with interactive API docs at `http://localhost:8000/docs`.

---

## 🛠 API Endpoints

| Endpoint | Method | Description |
| ----------- | ------ | --------------------------------- |
| `/` | GET | Health check |
| `/generate` | POST | Generate image from text prompt |
| `/segment` | POST | Segment objects in uploaded image |
| `/edit` | POST | Edit objects in uploaded image |
| `/depth` | POST | Generate depth map of image |

### Example Usage

**Generate an image:**
```bash
curl -X POST "http://localhost:8000/generate" \
     -H "Content-Type: application/json" \
     -d '{"prompt": "an astronaut riding a horse", "width": 1024, "height": 1024}'
```

**Quick generation via GET:**
```bash
curl "http://localhost:8000/generate?prompt=a%20beautiful%20sunset"
```

**Segment an image:**
```bash
curl -X POST "http://localhost:8000/segment" \
     -H "Content-Type: application/json" \
     -d '{"image": "<base64_encoded_image>", "mode": "auto"}'
```

**Edit an image:**
```bash
curl -X POST "http://localhost:8000/edit" \
     -H "Content-Type: application/json" \
     -d '{
       "image": "<base64_encoded_image>",
       "mode": "replace",
       "prompt": "a red car",
       "points": [[100, 200]],
       "labels": [1]
     }'
```

**Generate depth map:**
```bash
curl -X POST "http://localhost:8000/depth" \
     -H "Content-Type: application/json" \
     -d '{"image": "<base64_encoded_image>", "colormap": "plasma"}'
```

---

## 📂 Project Structure

```
smart_visual_gen_v2_python/
├── main.py                  # FastAPI entry point
├── api/
│   ├── routes.py            # API route handlers
│   └── models.py            # Pydantic request/response models
├── core/                    # AI logic
│   ├── text_to_image.py     # SDXL text-to-image generation
│   ├── segmentation.py      # SAM segmentation
│   ├── editing.py           # Grounded-SAM + inpainting
│   ├── depth_estimation.py  # MiDaS depth mapping
│   └── utils.py             # Utility functions
├── config/
│   └── settings.py          # Application settings
├── scripts/
│   └── download_models.py   # Downloads models
├── models/                  # Stores model files
├── requirements.txt         # Python dependencies
├── .env                     # Configuration
└── README.md               # This file
```

---

## 🔧 Configuration

Edit `.env` file to customize settings:

```env
# Model Storage
MODEL_CACHE_DIR=./models
HUGGINGFACE_CACHE_DIR=./models/huggingface

# Device Configuration
DEFAULT_DEVICE=cuda
ENABLE_CPU_OFFLOAD=true
GPU_MEMORY_LIMIT=12

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false

# Generation Settings
DEFAULT_STEPS=30
DEFAULT_GUIDANCE_SCALE=7.5
DEFAULT_WIDTH=1024
DEFAULT_HEIGHT=1024
MAX_WIDTH=1536
MAX_HEIGHT=1536
```

---

## 🚨 Troubleshooting

### ✅ CUDA Out of Memory
- Enable CPU offload in `.env`: `ENABLE_CPU_OFFLOAD=true`
- Lower GPU memory limit: `GPU_MEMORY_LIMIT=8`
- Close other GPU-heavy applications
- Reduce image dimensions

### ✅ Slow generation?
- Check if GPU is being used: visit `/info` endpoint
- Ensure CUDA is properly installed
- Consider using smaller models for testing

### ✅ Model download issues?
- Ensure you have ~50GB free space
- Check your internet connection
- Try running `python scripts/download_models.py` again

### ✅ Import errors?
- Make sure virtual environment is activated
- Reinstall requirements: `pip install -r requirements.txt --force-reinstall`
- Check Python version (3.9+ required)

---

## 🎯 Advanced Usage

### Custom Model Configuration

You can modify model settings in `config/settings.py`:

```python
# Use different SDXL models
sdxl_model_id: str = "stabilityai/stable-diffusion-xl-base-1.0"
sdxl_refiner_id: str = "stabilityai/stable-diffusion-xl-refiner-1.0"

# Use different SAM model sizes
sam_model_type: str = "vit_h"  # vit_h, vit_l, or vit_b
```

### Batch Processing

The API supports generating multiple images at once:

```json
{
  "prompt": "a beautiful landscape",
  "num_images": 4,
  "seed": 42
}
```

### Memory Optimization

For systems with limited VRAM:

1. Enable CPU offload: `ENABLE_CPU_OFFLOAD=true`
2. Use attention slicing: `MEMORY_EFFICIENT_ATTENTION=true`
3. Reduce batch size: `MAX_BATCH_SIZE=1`

---

## 📝 License

MIT License — free for personal and commercial use.

---

## 🤝 Contributing

Pull requests and ideas are welcome!

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

## 🙏 Acknowledgments

- **Stability AI** for Stable Diffusion XL
- **Meta AI** for Segment Anything Model
- **Intel** for MiDaS depth estimation
- **Hugging Face** for model hosting and diffusers library

---

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Visit the `/info` endpoint for system diagnostics
3. Check logs for detailed error messages
4. Open an issue on GitHub with system info and error logs
