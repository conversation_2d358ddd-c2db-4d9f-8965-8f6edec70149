"""
FastAPI routes for Smart Visual Generation System v2.0
"""
import time
import logging
from fastapi import APIRouter, HTTPException, UploadFile, File, Query
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Optional, List
import cv2

from api.models import (
    GenerateRequest, GenerateResponse,
    SegmentRequest, SegmentResponse,
    EditRequest, EditResponse,
    DepthRequest, DepthResponse,
    HealthResponse, ErrorResponse,
    ColorMapType
)
from core.text_to_image import TextToImageGenerator
from core.segmentation import ImageSegmenter
from core.editing import ImageEditor
from core.depth_estimation import DepthEstimator
from core.utils import (
    pil_to_base64, base64_to_pil, get_memory_usage, setup_device
)
from config.settings import settings

logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter()

# Global model instances (lazy loaded)
text_to_image_generator = None
image_segmenter = None
image_editor = None
depth_estimator = None


def get_text_to_image_generator():
    """Get or create text-to-image generator"""
    global text_to_image_generator
    if text_to_image_generator is None:
        text_to_image_generator = TextToImageGenerator()
    return text_to_image_generator


def get_image_segmenter():
    """Get or create image segmenter"""
    global image_segmenter
    if image_segmenter is None:
        image_segmenter = ImageSegmenter()
    return image_segmenter


def get_image_editor():
    """Get or create image editor"""
    global image_editor
    if image_editor is None:
        image_editor = ImageEditor()
    return image_editor


def get_depth_estimator():
    """Get or create depth estimator"""
    global depth_estimator
    if depth_estimator is None:
        depth_estimator = DepthEstimator()
    return depth_estimator


@router.get("/", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        device = setup_device(settings.default_device)
        memory_info = get_memory_usage()
        
        # Check which models are loaded
        models_loaded = {
            "text_to_image": text_to_image_generator is not None,
            "segmentation": image_segmenter is not None,
            "editing": image_editor is not None,
            "depth_estimation": depth_estimator is not None
        }
        
        return HealthResponse(
            status="healthy",
            device=str(device),
            memory_info=memory_info,
            models_loaded=models_loaded
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate", response_model=GenerateResponse)
async def generate_image(request: GenerateRequest):
    """Generate image from text prompt"""
    try:
        start_time = time.time()
        
        # Get generator
        generator = get_text_to_image_generator()
        
        # Generate images
        images = generator.generate(
            prompt=request.prompt,
            negative_prompt=request.negative_prompt,
            width=request.width,
            height=request.height,
            num_inference_steps=request.num_inference_steps,
            guidance_scale=request.guidance_scale,
            num_images=request.num_images,
            seed=request.seed,
            use_refiner=request.use_refiner
        )
        
        # Convert to base64
        image_b64_list = [pil_to_base64(img) for img in images]
        
        generation_time = time.time() - start_time
        
        return GenerateResponse(
            images=image_b64_list,
            seed_used=request.seed,
            generation_time=generation_time
        )
        
    except Exception as e:
        logger.error(f"Image generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/segment", response_model=SegmentResponse)
async def segment_image(request: SegmentRequest):
    """Segment objects in image"""
    try:
        start_time = time.time()
        
        # Get segmenter
        segmenter = get_image_segmenter()
        
        # Decode input image
        image = base64_to_pil(request.image)
        
        if request.mode == "auto":
            # Automatic segmentation
            masks_data = segmenter.segment_everything(image)
            
            # Convert masks to base64
            mask_images = []
            for mask_data in masks_data[:10]:  # Limit to top 10 masks
                mask_img = segmenter.create_mask_overlay(
                    image, mask_data['segmentation']
                )
                mask_images.append(pil_to_base64(mask_img))
            
            overlay = None
            if request.return_overlay and masks_data:
                # Create combined overlay
                overlay_img = image.copy()
                for i, mask_data in enumerate(masks_data[:5]):  # Top 5 for overlay
                    color = (255, 0, 0) if i == 0 else (0, 255, 0)
                    overlay_img = segmenter.create_mask_overlay(
                        overlay_img, mask_data['segmentation'], color, alpha=0.3
                    )
                overlay = pil_to_base64(overlay_img)
            
            num_segments = len(masks_data)
            
        elif request.mode == "points":
            if not request.points or not request.labels:
                raise HTTPException(
                    status_code=400,
                    detail="Points and labels are required for point-based segmentation"
                )
            
            # Point-based segmentation
            masks, scores, _ = segmenter.segment_with_points(
                image, request.points, request.labels
            )
            
            # Convert masks to base64
            mask_images = []
            for mask in masks:
                mask_img = segmenter.create_mask_overlay(image, mask)
                mask_images.append(pil_to_base64(mask_img))
            
            overlay = None
            if request.return_overlay and len(masks) > 0:
                overlay = pil_to_base64(
                    segmenter.create_mask_overlay(image, masks[0])
                )
            
            num_segments = len(masks)
            
        elif request.mode == "box":
            if not request.box:
                raise HTTPException(
                    status_code=400,
                    detail="Bounding box is required for box-based segmentation"
                )
            
            # Box-based segmentation
            masks, scores, _ = segmenter.segment_with_box(image, request.box)
            
            # Convert masks to base64
            mask_images = []
            for mask in masks:
                mask_img = segmenter.create_mask_overlay(image, mask)
                mask_images.append(pil_to_base64(mask_img))
            
            overlay = None
            if request.return_overlay and len(masks) > 0:
                overlay = pil_to_base64(
                    segmenter.create_mask_overlay(image, masks[0])
                )
            
            num_segments = len(masks)
            
        else:
            raise HTTPException(
                status_code=400,
                detail="Invalid segmentation mode. Use 'auto', 'points', or 'box'"
            )
        
        processing_time = time.time() - start_time
        
        return SegmentResponse(
            masks=mask_images,
            overlay=overlay,
            num_segments=num_segments,
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Image segmentation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/edit", response_model=EditResponse)
async def edit_image(request: EditRequest):
    """Edit image using inpainting and segmentation"""
    try:
        start_time = time.time()

        # Get editor
        editor = get_image_editor()

        # Decode input image
        image = base64_to_pil(request.image)

        if request.mode == "inpaint":
            if not request.mask:
                raise HTTPException(
                    status_code=400,
                    detail="Mask is required for inpainting mode"
                )

            # Direct inpainting with provided mask
            mask = base64_to_pil(request.mask)
            edited_image = editor.inpaint_region(
                image=image,
                mask=mask,
                prompt=request.prompt,
                negative_prompt=request.negative_prompt,
                num_inference_steps=request.num_inference_steps,
                guidance_scale=request.guidance_scale,
                strength=request.strength,
                seed=request.seed
            )
            mask_used = mask

        elif request.mode == "remove":
            if not request.points or not request.labels:
                raise HTTPException(
                    status_code=400,
                    detail="Points and labels are required for remove mode"
                )

            # Remove object using point-based segmentation
            edited_image, mask_used = editor.remove_object(
                image=image,
                points=request.points,
                labels=request.labels,
                inpaint_prompt=request.prompt,
                negative_prompt=request.negative_prompt
            )

        elif request.mode == "replace":
            if not request.points or not request.labels:
                raise HTTPException(
                    status_code=400,
                    detail="Points and labels are required for replace mode"
                )

            # Replace object using point-based segmentation
            edited_image, mask_used = editor.replace_object(
                image=image,
                points=request.points,
                labels=request.labels,
                replacement_prompt=request.prompt,
                negative_prompt=request.negative_prompt
            )

        else:
            raise HTTPException(
                status_code=400,
                detail="Invalid edit mode. Use 'inpaint', 'remove', or 'replace'"
            )

        processing_time = time.time() - start_time

        return EditResponse(
            edited_image=pil_to_base64(edited_image),
            mask_used=pil_to_base64(mask_used),
            processing_time=processing_time
        )

    except Exception as e:
        logger.error(f"Image editing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/depth", response_model=DepthResponse)
async def estimate_depth(request: DepthRequest):
    """Estimate depth map from image"""
    try:
        start_time = time.time()

        # Get depth estimator
        estimator = get_depth_estimator()

        # Decode input image
        image = base64_to_pil(request.image)

        # Set output size
        output_size = None
        if request.output_width and request.output_height:
            output_size = (request.output_width, request.output_height)

        # Estimate depth
        depth_map = estimator.estimate_depth(image, output_size)

        # Convert colormap enum to OpenCV constant
        colormap_mapping = {
            ColorMapType.plasma: cv2.COLORMAP_PLASMA,
            ColorMapType.viridis: cv2.COLORMAP_VIRIDIS,
            ColorMapType.jet: cv2.COLORMAP_JET,
            ColorMapType.hot: cv2.COLORMAP_HOT,
            ColorMapType.cool: cv2.COLORMAP_COOL,
            ColorMapType.spring: cv2.COLORMAP_SPRING,
            ColorMapType.summer: cv2.COLORMAP_SUMMER,
            ColorMapType.autumn: cv2.COLORMAP_AUTUMN,
            ColorMapType.winter: cv2.COLORMAP_WINTER,
        }
        colormap = colormap_mapping[request.colormap]

        # Create colored depth visualization
        depth_image = estimator.depth_to_image(depth_map, colormap)

        # Prepare response
        response_data = {
            "depth_image": pil_to_base64(depth_image),
            "processing_time": time.time() - start_time
        }

        # Add raw depth if requested
        if request.return_raw:
            import numpy as np
            from core.utils import numpy_to_pil
            # Convert depth map to 16-bit grayscale for better precision
            depth_16bit = (depth_map * 65535).astype(np.uint16)
            depth_pil = numpy_to_pil(depth_16bit)
            response_data["raw_depth"] = pil_to_base64(depth_pil)

        # Add 3D effect if requested
        if request.create_3d_effect:
            effect_image = estimator.create_3d_effect(
                image, depth_map, request.displacement_strength
            )
            response_data["effect_image"] = pil_to_base64(effect_image)

        return DepthResponse(**response_data)

    except Exception as e:
        logger.error(f"Depth estimation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Additional utility endpoints
@router.get("/generate")
async def generate_image_get(
    prompt: str = Query(..., description="Text prompt for image generation"),
    negative_prompt: Optional[str] = Query(None, description="Negative prompt"),
    width: Optional[int] = Query(1024, ge=256, le=1536, description="Image width"),
    height: Optional[int] = Query(1024, ge=256, le=1536, description="Image height"),
    steps: Optional[int] = Query(30, ge=10, le=100, description="Inference steps"),
    guidance: Optional[float] = Query(7.5, ge=1.0, le=20.0, description="Guidance scale"),
    seed: Optional[int] = Query(None, description="Random seed")
):
    """Generate image via GET request (for simple testing)"""
    request = GenerateRequest(
        prompt=prompt,
        negative_prompt=negative_prompt,
        width=width,
        height=height,
        num_inference_steps=steps,
        guidance_scale=guidance,
        seed=seed
    )
    return await generate_image(request)
