#!/usr/bin/env python3
"""
Smart Visual Generation System v2.0 - Main FastAPI Application
An AI-powered creative suite for intelligent image generation, segmentation, editing, and 3D depth enhancement
"""
import logging
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager

from config.settings import settings
from api.routes import router
from api.models import ErrorResponse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("🚀 Starting Smart Visual Generation System v2.0")
    logger.info(f"Device: {settings.default_device}")
    logger.info(f"Model cache: {settings.model_cache_dir}")
    
    # Ensure directories exist
    settings.ensure_directories()
    
    # Check if SAM checkpoint exists
    if not settings.sam_checkpoint_path.exists():
        logger.warning(
            f"SAM checkpoint not found at {settings.sam_checkpoint_path}. "
            "Please run 'python scripts/download_models.py' first."
        )
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down Smart Visual Generation System v2.0")


# Create FastAPI app
app = FastAPI(
    title="Smart Visual Generation System v2.0",
    description="""
    An AI-powered creative suite for intelligent image generation, segmentation, editing, and 3D depth enhancement.
    
    ## Features
    
    🎨 **Text-to-Image Generation** — Create high-quality images from text using Stable Diffusion XL (SDXL).
    
    🧠 **Automatic Segmentation** — Detect and outline objects using Segment Anything Model (SAM).
    
    👆 **Region Editing** — Edit, remove, or modify objects precisely with Grounded-SAM + SDXL inpainting.
    
    🌄 **Depth Estimation** — Convert 2D images to depth maps with MiDaS (DPT Hybrid).
    
    🔒 **Offline & Private** — All models run locally. No API keys, no data leaves your machine.
    
    ## Quick Start
    
    1. **Generate an image**: `POST /generate` with a text prompt
    2. **Segment objects**: `POST /segment` with an image
    3. **Edit regions**: `POST /edit` with an image and edit instructions
    4. **Estimate depth**: `POST /depth` with an image
    
    ## Example Usage
    
    ```bash
    # Generate an image
    curl -X POST "http://localhost:8000/generate" \\
         -H "Content-Type: application/json" \\
         -d '{"prompt": "an astronaut riding a horse"}'
    
    # Quick generation via GET
    curl "http://localhost:8000/generate?prompt=a%20beautiful%20sunset"
    ```
    """,
    version="2.0.0",
    contact={
        "name": "Smart Visual Generation System",
        "url": "https://github.com/your-repo/smart-visual-gen-v2",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(_request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error",
            detail=str(exc) if settings.debug else "An unexpected error occurred"
        ).dict()
    )


# Additional endpoints for convenience
@app.get("/health")
async def health():
    """Simple health check"""
    return {"status": "healthy", "version": "2.0.0"}


@app.get("/info")
async def system_info():
    """Get system information"""
    import torch
    from core.utils import get_memory_usage
    
    info = {
        "version": "2.0.0",
        "device": settings.default_device,
        "torch_version": torch.__version__,
        "cuda_available": torch.cuda.is_available(),
        "memory_info": get_memory_usage(),
        "settings": {
            "model_cache_dir": str(settings.model_cache_dir),
            "default_steps": settings.default_steps,
            "default_guidance_scale": settings.default_guidance_scale,
            "max_width": settings.max_width,
            "max_height": settings.max_height,
        }
    }
    
    if torch.cuda.is_available():
        info["gpu_info"] = {
            "device_name": torch.cuda.get_device_name(),
            "device_count": torch.cuda.device_count(),
            "current_device": torch.cuda.current_device(),
        }
    
    return info


def main():
    """Main function to run the server"""
    logger.info("🎨 Smart Visual Generation System v2.0")
    logger.info("=" * 50)
    logger.info("An AI-powered creative suite for:")
    logger.info("  🎨 Text-to-Image Generation (SDXL)")
    logger.info("  🧠 Automatic Segmentation (SAM)")
    logger.info("  👆 Region Editing (Grounded-SAM + Inpainting)")
    logger.info("  🌄 Depth Estimation (MiDaS)")
    logger.info("=" * 50)
    
    # Check if models are downloaded
    if not settings.sam_checkpoint_path.exists():
        logger.error("❌ SAM models not found!")
        logger.error("Please run: python scripts/download_models.py")
        return
    
    logger.info(f"🚀 Starting server on {settings.host}:{settings.port}")
    logger.info(f"📖 API docs: http://{settings.host}:{settings.port}/docs")
    logger.info(f"🔧 Device: {settings.default_device}")
    
    # Run the server
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )


if __name__ == "__main__":
    main()
