"""
Depth estimation using MiDaS (DPT Hybrid)
"""
import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import cv2
from typing import Optional, Tuple, List
import logging
from config.settings import settings
from core.utils import setup_device, pil_to_numpy, numpy_to_pil, clear_gpu_memory

logger = logging.getLogger(__name__)


class DepthEstimator:
    """MiDaS-based depth estimation"""
    
    def __init__(self, device: Optional[torch.device] = None):
        self.device = device or setup_device(settings.default_device)
        self.model = None
        self.transform = None
        self._load_model()
    
    def _load_model(self):
        """Load MiDaS model"""
        try:
            logger.info(f"Loading MiDaS model ({settings.midas_model_type})...")
            
            # Load model from torch hub
            self.model = torch.hub.load(
                'intel-isl/MiDaS',
                settings.midas_model_type,
                pretrained=True,
                trust_repo=True
            )
            
            # Load transforms
            midas_transforms = torch.hub.load(
                'intel-isl/MiDaS',
                'transforms',
                trust_repo=True
            )
            
            if settings.midas_model_type == "DPT_Large" or settings.midas_model_type == "DPT_Hybrid":
                self.transform = midas_transforms.dpt_transform
            else:
                self.transform = midas_transforms.small_transform
            
            # Move model to device and set to eval mode
            self.model.to(self.device)
            self.model.eval()
            
            logger.info("MiDaS model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load MiDaS model: {e}")
            raise
    
    def estimate_depth(
        self,
        image: Image.Image,
        output_size: Optional[Tuple[int, int]] = None
    ) -> np.ndarray:
        """
        Estimate depth map from image
        
        Args:
            image: PIL Image
            output_size: Optional output size (width, height)
            
        Returns:
            Depth map as numpy array (normalized 0-1)
        """
        try:
            logger.info("Estimating depth map...")
            
            # Convert PIL to numpy and ensure RGB
            image_rgb = image.convert('RGB')
            
            # Apply transforms
            input_batch = self.transform(image_rgb).to(self.device)
            
            # Predict depth
            with torch.no_grad():
                prediction = self.model(input_batch)
                
                # Interpolate to original size
                prediction = F.interpolate(
                    prediction.unsqueeze(1),
                    size=image_rgb.size[::-1],  # (height, width)
                    mode="bicubic",
                    align_corners=False,
                ).squeeze()
            
            # Convert to numpy
            depth_map = prediction.cpu().numpy()
            
            # Normalize to 0-1 range
            depth_map = (depth_map - depth_map.min()) / (depth_map.max() - depth_map.min())
            
            # Resize if output size specified
            if output_size:
                depth_map = cv2.resize(
                    depth_map,
                    output_size,
                    interpolation=cv2.INTER_CUBIC
                )
            
            logger.info("Depth estimation completed")
            return depth_map
            
        except Exception as e:
            logger.error(f"Failed to estimate depth: {e}")
            raise
        finally:
            clear_gpu_memory()
    
    def depth_to_image(
        self,
        depth_map: np.ndarray,
        colormap: int = cv2.COLORMAP_PLASMA
    ) -> Image.Image:
        """
        Convert depth map to colored visualization
        
        Args:
            depth_map: Depth map array (0-1 normalized)
            colormap: OpenCV colormap for visualization
            
        Returns:
            Colored depth map as PIL Image
        """
        # Convert to 8-bit
        depth_8bit = (depth_map * 255).astype(np.uint8)
        
        # Apply colormap
        colored_depth = cv2.applyColorMap(depth_8bit, colormap)
        
        # Convert BGR to RGB
        colored_depth = cv2.cvtColor(colored_depth, cv2.COLOR_BGR2RGB)
        
        return numpy_to_pil(colored_depth)
    
    def create_3d_effect(
        self,
        image: Image.Image,
        depth_map: np.ndarray,
        displacement_strength: float = 10.0
    ) -> Image.Image:
        """
        Create a 3D displacement effect using the depth map
        
        Args:
            image: Original PIL Image
            depth_map: Depth map array (0-1 normalized)
            displacement_strength: Strength of the 3D effect
            
        Returns:
            PIL Image with 3D effect
        """
        # Convert image to numpy
        img_array = pil_to_numpy(image.convert('RGB'))
        h, w = img_array.shape[:2]
        
        # Ensure depth map matches image size
        if depth_map.shape != (h, w):
            depth_map = cv2.resize(depth_map, (w, h), interpolation=cv2.INTER_CUBIC)
        
        # Create displacement maps
        displacement = (depth_map - 0.5) * displacement_strength
        
        # Create coordinate grids
        y_coords, x_coords = np.mgrid[0:h, 0:w]
        
        # Apply displacement
        x_displaced = x_coords + displacement
        y_displaced = y_coords
        
        # Clip coordinates to valid range
        x_displaced = np.clip(x_displaced, 0, w - 1)
        y_displaced = np.clip(y_displaced, 0, h - 1)
        
        # Remap the image
        displaced_image = cv2.remap(
            img_array,
            x_displaced.astype(np.float32),
            y_displaced.astype(np.float32),
            cv2.INTER_LINEAR
        )
        
        return numpy_to_pil(displaced_image)
    
    def create_depth_layers(
        self,
        image: Image.Image,
        depth_map: np.ndarray,
        num_layers: int = 5
    ) -> List[Image.Image]:
        """
        Create depth-based layers for parallax effects
        
        Args:
            image: Original PIL Image
            depth_map: Depth map array (0-1 normalized)
            num_layers: Number of depth layers to create
            
        Returns:
            List of PIL Images representing different depth layers
        """
        layers = []
        img_array = pil_to_numpy(image.convert('RGBA'))
        
        # Create layers based on depth thresholds
        for i in range(num_layers):
            depth_min = i / num_layers
            depth_max = (i + 1) / num_layers
            
            # Create mask for this depth range
            mask = (depth_map >= depth_min) & (depth_map < depth_max)
            
            # Apply mask to create layer
            layer = img_array.copy()
            layer[:, :, 3] = mask.astype(np.uint8) * 255
            
            layers.append(numpy_to_pil(layer))
        
        return layers
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        clear_gpu_memory()
