# Core FastAPI and web server
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
python-dotenv==1.0.0
pydantic-settings==2.0.3

# AI/ML Core Libraries
torch>=2.1.0
torchvision>=0.16.0
transformers>=4.35.0
diffusers>=0.24.0
accelerate>=0.24.0

# Computer Vision
opencv-python==********
Pillow>=10.0.0
numpy>=1.24.0
scipy>=1.11.0

# Segmentation (SAM)
segment-anything @ git+https://github.com/facebookresearch/segment-anything.git
groundingdino-py>=0.4.0

# Depth Estimation (MiDaS)
timm>=0.9.0

# Image Processing
imageio>=2.31.0
scikit-image>=0.21.0

# Utilities
requests>=2.31.0
tqdm>=4.66.0
matplotlib>=3.7.0

# Optional GPU acceleration
xformers>=0.0.22; sys_platform != "darwin"

# Development
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0
