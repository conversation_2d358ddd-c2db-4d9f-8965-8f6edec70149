#!/usr/bin/env python3
"""
Test script to verify Smart Visual Generation System v2.0 installation
"""
import sys
import importlib
from pathlib import Path

def test_imports():
    """Test if all required packages can be imported"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'torch',
        'torchvision',
        'transformers',
        'diffusers',
        'PIL',
        'numpy',
        'cv2',
        'requests',
        'tqdm'
    ]
    
    print("Testing package imports...")
    failed_imports = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError as e:
            print(f"❌ {package}: {e}")
            failed_imports.append(package)
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        print("Please run: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ All packages imported successfully!")
        return True

def test_torch():
    """Test PyTorch installation and CUDA availability"""
    try:
        import torch
        print(f"\n🔥 PyTorch version: {torch.__version__}")
        print(f"🔥 CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"🔥 CUDA version: {torch.version.cuda}")
            print(f"🔥 GPU count: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"🔥 GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("⚠️  CUDA not available - will use CPU (slower)")
        
        return True
    except Exception as e:
        print(f"❌ PyTorch test failed: {e}")
        return False

def test_config():
    """Test configuration loading"""
    try:
        from config.settings import settings
        print(f"\n⚙️  Configuration loaded successfully")
        print(f"⚙️  Model cache dir: {settings.model_cache_dir}")
        print(f"⚙️  Default device: {settings.default_device}")
        print(f"⚙️  Server port: {settings.port}")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_models_downloaded():
    """Test if required models are downloaded"""
    try:
        from config.settings import settings
        
        print(f"\n📦 Checking model files...")
        
        # Check SAM checkpoint
        sam_path = settings.sam_checkpoint_path
        if sam_path.exists():
            print(f"✅ SAM checkpoint found: {sam_path}")
        else:
            print(f"❌ SAM checkpoint missing: {sam_path}")
            print("Please run: python scripts/download_models.py")
            return False
        
        # Check model directories
        model_dir = Path(settings.model_cache_dir)
        if model_dir.exists():
            print(f"✅ Model cache directory exists: {model_dir}")
        else:
            print(f"❌ Model cache directory missing: {model_dir}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Model check failed: {e}")
        return False

def test_api_imports():
    """Test if API components can be imported"""
    try:
        from api.routes import router
        from api.models import GenerateRequest
        from core.utils import setup_device
        print(f"\n🌐 API components imported successfully")
        return True
    except Exception as e:
        print(f"❌ API import test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Smart Visual Generation System v2.0 - Installation Test")
    print("=" * 60)
    
    tests = [
        ("Package Imports", test_imports),
        ("PyTorch & CUDA", test_torch),
        ("Configuration", test_config),
        ("API Components", test_api_imports),
        ("Model Files", test_models_downloaded),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"🧪 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Your installation is ready.")
        print("🚀 You can now run: python main.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        if passed < 3:
            print("💡 Try running: pip install -r requirements.txt")
        if "Model Files" in [tests[i][0] for i in range(len(tests)) if i >= passed]:
            print("💡 Try running: python scripts/download_models.py")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
