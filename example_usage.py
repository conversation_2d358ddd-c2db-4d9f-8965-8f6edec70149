#!/usr/bin/env python3
"""
Example usage of Smart Visual Generation System v2.0 API
"""
import requests
import base64
import json
from PIL import Image
from io import BytesIO
import time

# API base URL
BASE_URL = "http://localhost:8000"

def save_base64_image(base64_str: str, filename: str):
    """Save base64 encoded image to file"""
    image_data = base64.b64decode(base64_str)
    image = Image.open(BytesIO(image_data))
    image.save(filename)
    print(f"💾 Saved image: {filename}")

def image_to_base64(image_path: str) -> str:
    """Convert image file to base64 string"""
    with open(image_path, "rb") as img_file:
        return base64.b64encode(img_file.read()).decode()

def test_health():
    """Test health endpoint"""
    print("🏥 Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Server is healthy")
            print(f"   Device: {data.get('device', 'unknown')}")
            print(f"   Models loaded: {data.get('models_loaded', {})}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_generate():
    """Test image generation"""
    print("\n🎨 Testing image generation...")
    try:
        prompt = "a beautiful sunset over mountains, digital art, highly detailed"
        
        payload = {
            "prompt": prompt,
            "negative_prompt": "blurry, low quality, distorted",
            "width": 512,  # Smaller size for faster testing
            "height": 512,
            "num_inference_steps": 20,  # Fewer steps for faster testing
            "guidance_scale": 7.5,
            "seed": 42
        }
        
        print(f"   Prompt: {prompt}")
        print("   Generating image... (this may take a while)")
        
        start_time = time.time()
        response = requests.post(f"{BASE_URL}/generate", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            generation_time = time.time() - start_time
            
            print(f"✅ Image generated successfully in {generation_time:.1f}s")
            
            # Save the generated image
            if data["images"]:
                save_base64_image(data["images"][0], "generated_image.png")
                return "generated_image.png"
            
        else:
            print(f"❌ Generation failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return None

def test_segment(image_path: str):
    """Test image segmentation"""
    print(f"\n🧠 Testing image segmentation on {image_path}...")
    try:
        # Convert image to base64
        image_b64 = image_to_base64(image_path)
        
        payload = {
            "image": image_b64,
            "mode": "auto",
            "return_overlay": True
        }
        
        print("   Segmenting image...")
        response = requests.post(f"{BASE_URL}/segment", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Segmentation completed")
            print(f"   Found {data['num_segments']} segments")
            print(f"   Processing time: {data['processing_time']:.1f}s")
            
            # Save overlay if available
            if data.get("overlay"):
                save_base64_image(data["overlay"], "segmentation_overlay.png")
            
            return True
        else:
            print(f"❌ Segmentation failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Segmentation error: {e}")
        return False

def test_depth(image_path: str):
    """Test depth estimation"""
    print(f"\n🌄 Testing depth estimation on {image_path}...")
    try:
        # Convert image to base64
        image_b64 = image_to_base64(image_path)
        
        payload = {
            "image": image_b64,
            "colormap": "plasma",
            "create_3d_effect": True,
            "displacement_strength": 15.0
        }
        
        print("   Estimating depth...")
        response = requests.post(f"{BASE_URL}/depth", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Depth estimation completed")
            print(f"   Processing time: {data['processing_time']:.1f}s")
            
            # Save depth map
            save_base64_image(data["depth_image"], "depth_map.png")
            
            # Save 3D effect if available
            if data.get("effect_image"):
                save_base64_image(data["effect_image"], "3d_effect.png")
            
            return True
        else:
            print(f"❌ Depth estimation failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Depth estimation error: {e}")
        return False

def test_quick_generate():
    """Test quick generation via GET endpoint"""
    print("\n⚡ Testing quick generation (GET endpoint)...")
    try:
        params = {
            "prompt": "a cute robot, cartoon style",
            "width": 512,
            "height": 512,
            "steps": 15,
            "seed": 123
        }
        
        print(f"   Prompt: {params['prompt']}")
        response = requests.get(f"{BASE_URL}/generate", params=params)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Quick generation successful")
            
            if data["images"]:
                save_base64_image(data["images"][0], "quick_generated.png")
            
            return True
        else:
            print(f"❌ Quick generation failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Quick generation error: {e}")
        return False

def main():
    """Run all example tests"""
    print("🧪 Smart Visual Generation System v2.0 - API Examples")
    print("=" * 60)
    print("Make sure the server is running: python main.py")
    print("=" * 60)
    
    # Test health first
    if not test_health():
        print("\n❌ Server is not responding. Please start it with: python main.py")
        return
    
    # Test image generation
    generated_image = test_generate()
    
    # Test quick generation
    test_quick_generate()
    
    # Test segmentation and depth if we have an image
    if generated_image:
        test_segment(generated_image)
        test_depth(generated_image)
    else:
        print("\n⚠️  Skipping segmentation and depth tests (no generated image)")
    
    print("\n" + "=" * 60)
    print("🎉 Example tests completed!")
    print("Check the generated files:")
    print("  - generated_image.png")
    print("  - quick_generated.png")
    print("  - segmentation_overlay.png")
    print("  - depth_map.png")
    print("  - 3d_effect.png")
    print("\n📖 For more examples, visit: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
